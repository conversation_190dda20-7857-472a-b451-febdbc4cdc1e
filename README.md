# OhMyKing's Home

A static website project featuring interactive animations and modern web design.

## 项目结构

```
├── index.html          # 主页面
├── css/               # 样式文件
│   ├── styles.css     # 主样式
│   └── css2.css       # 字体样式
├── js/                # JavaScript文件
│   ├── page4.js       # 第4页逻辑
│   ├── page5.js       # 第5页逻辑
│   ├── page6.js       # 第6页逻辑
│   ├── page7.js       # 第7页逻辑
│   └── ...            # 其他JS库
├── images/            # 图片资源
├── assets/            # 字体和图标
├── fonts/             # 字体文件
└── personal_site/     # 个人站点相关文件
```

## 启动方式

### 使用 npm (推荐)

1. 确保已安装 Node.js (版本 >= 14.0.0)

2. 安装依赖：
   ```bash
   npm install
   ```

3. 启动开发服务器：
   ```bash
   npm start
   ```
   或者
   ```bash
   npm run dev
   ```

4. 浏览器会自动打开 http://localhost:8000

### 可用的 npm 脚本

- `npm start` - 启动开发服务器并自动打开浏览器
- `npm run dev` - 启动开发服务器并自动打开浏览器 (与start相同)
- `npm run serve` - 启动服务器但不自动打开浏览器
- `npm run build` - 构建项目 (静态站点无需构建)
- `npm test` - 运行测试 (当前无测试)

### 使用 Python (旧方式)

如果您仍想使用Python服务器：

```bash
python server.py
```

然后访问 http://localhost:8000

## 功能特性

- 响应式设计
- 交互式动画效果
- 移动端适配
- 现代化UI设计
- 多页面导航

## 技术栈

- HTML5
- CSS3
- JavaScript (ES6+)
- Three.js (3D图形)
- GSAP (动画库)
- 其他第三方库

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 注意事项

- 建议使用桌面浏览器访问以获得最佳体验
- 移动端会显示提示信息
- 需要现代浏览器支持ES6+特性

## 开发

项目使用静态文件结构，修改文件后刷新浏览器即可看到更改。

服务器配置了无缓存头部，确保开发时能看到最新更改。
