<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X Oberon - Scroll Demo</title>
    <style>
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            scrollbar-width: none;
            -ms-overflow-style: none;
            background-color: #1a1a1a;
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
            color: white;
        }

        /* 滚动容器 */
        .scroll-container {
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
        }

        /* 页面section */
        .page-section {
            width: 100%;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }

        .page-section.active-section {
            transform: scale(1);
            opacity: 1;
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* 页面样式 */
        #page1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #page2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        #page3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        #page4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .page-content {
            text-align: center;
            max-width: 800px;
            padding: 40px;
        }

        .page-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .page-subtitle {
            font-size: 1.5rem;
            opacity: 0.8;
            margin-bottom: 30px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease 0.2s;
        }

        .page-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease 0.4s;
        }

        /* 激活状态的动画 */
        .page-section.active-section .page-title,
        .page-section.active-section .page-subtitle,
        .page-section.active-section .page-description {
            opacity: 1;
            transform: translateY(0);
        }

        /* 滚动指示器 */
        .scroll-indicator {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
        }

        .scroll-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .scroll-dot.active {
            background: white;
            transform: scale(1.2);
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2.5rem;
            }
            
            .page-subtitle {
                font-size: 1.2rem;
            }
            
            .navbar {
                top: 10px;
                right: 10px;
                gap: 10px;
            }
            
            .nav-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="navbar">
        <div class="nav-btn" onclick="scrollToSection(0)">Home</div>
        <div class="nav-btn" onclick="scrollToSection(1)">About</div>
        <div class="nav-btn" onclick="scrollToSection(2)">Skills</div>
        <div class="nav-btn" onclick="scrollToSection(3)">Contact</div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator">
        <div class="scroll-dot active" onclick="scrollToSection(0)"></div>
        <div class="scroll-dot" onclick="scrollToSection(1)"></div>
        <div class="scroll-dot" onclick="scrollToSection(2)"></div>
        <div class="scroll-dot" onclick="scrollToSection(3)"></div>
    </div>

    <!-- 滚动容器 -->
    <div class="scroll-container" id="scroll-container">
        <!-- 第1页 - 首页 -->
        <section class="page-section active-section" id="page1">
            <div class="page-content">
                <h1 class="page-title">X Oberon</h1>
                <p class="page-subtitle">Welcome to My Space</p>
                <p class="page-description">
                    Exploring the intersection of technology, creativity, and innovation. 
                    Scroll down to discover more about my journey and expertise.
                </p>
            </div>
        </section>

        <!-- 第2页 - 关于 -->
        <section class="page-section" id="page2">
            <div class="page-content">
                <h1 class="page-title">About Me</h1>
                <p class="page-subtitle">Passionate Developer & Creator</p>
                <p class="page-description">
                    I'm X Oberon, a dedicated professional focused on creating meaningful 
                    digital experiences. With a background in technology and design, 
                    I strive to build solutions that make a difference.
                </p>
            </div>
        </section>

        <!-- 第3页 - 技能 -->
        <section class="page-section" id="page3">
            <div class="page-content">
                <h1 class="page-title">Skills</h1>
                <p class="page-subtitle">What I Bring to the Table</p>
                <p class="page-description">
                    • Full-Stack Development<br>
                    • UI/UX Design<br>
                    • Creative Problem Solving<br>
                    • Team Leadership<br>
                    • Continuous Learning
                </p>
            </div>
        </section>

        <!-- 第4页 - 联系 -->
        <section class="page-section" id="page4">
            <div class="page-content">
                <h1 class="page-title">Get In Touch</h1>
                <p class="page-subtitle">Let's Create Something Amazing</p>
                <p class="page-description">
                    Ready to collaborate on your next project? 
                    I'm always excited to work with passionate individuals 
                    and teams who share a vision for excellence.
                </p>
            </div>
        </section>
    </div>

    <script>
        // 获取元素
        const scrollContainer = document.getElementById('scroll-container');
        const sections = document.querySelectorAll('.page-section');
        const scrollDots = document.querySelectorAll('.scroll-dot');
        let currentIndex = 0;

        // 滚动到指定页面
        function scrollToSection(index) {
            sections[index].scrollIntoView({ behavior: 'smooth' });
        }

        // 处理滚动事件
        function handleScroll() {
            const scrollPosition = scrollContainer.scrollTop;
            const windowHeight = window.innerHeight;

            // 检测当前页面
            sections.forEach((section, index) => {
                const sectionTop = section.offsetTop - 10;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    if (currentIndex !== index) {
                        // 更新当前页面
                        sections.forEach(s => s.classList.remove('active-section'));
                        section.classList.add('active-section');
                        
                        // 更新滚动指示器
                        scrollDots.forEach(dot => dot.classList.remove('active'));
                        scrollDots[index].classList.add('active');
                        
                        currentIndex = index;
                    }
                }
            });
        }

        // 监听滚动事件
        scrollContainer.addEventListener('scroll', handleScroll);

        // 初始化
        sections[0].classList.add('active-section');
        scrollDots[0].classList.add('active');

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
                scrollToSection(currentIndex + 1);
            } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                scrollToSection(currentIndex - 1);
            }
        });
    </script>
</body>
</html>
