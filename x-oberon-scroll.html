<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X Oberon - Scroll Demo</title>
    <style>
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            scrollbar-width: none;
            -ms-overflow-style: none;
            background-color: #1a1a1a;
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
            color: white;
        }

        /* 滚动容器 */
        .scroll-container {
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
        }

        /* 页面section */
        .page-section {
            width: 100%;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }

        .page-section.active-section {
            transform: scale(1);
            opacity: 1;
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* 页面样式 */
        .page-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        .page-content {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            position: relative;
        }

        /* 流动文字容器 */
        .flowing-text {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            text-align: center;
            pointer-events: none;
        }

        .main-title {
            font-size: 5rem;
            font-weight: bold;
            margin-bottom: 20px;
            transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
        }

        .sub-title {
            font-size: 1.8rem;
            opacity: 0.8;
            transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
        }

        .description {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-top: 30px;
            opacity: 0;
            transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 600px;
        }

        /* 背景文字效果 */
        .background-text {
            position: absolute;
            font-size: 8rem;
            font-weight: bold;
            opacity: 0.05;
            pointer-events: none;
            white-space: nowrap;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(100vw); }
            100% { transform: translateX(-100%); }
        }

        .bg-text-1 { top: 20%; animation-delay: 0s; }
        .bg-text-2 { top: 40%; animation-delay: -5s; }
        .bg-text-3 { top: 60%; animation-delay: -10s; }
        .bg-text-4 { top: 80%; animation-delay: -15s; }

        /* 滚动指示器 */
        .scroll-indicator {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
        }

        .scroll-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .scroll-dot.active {
            background: white;
            transform: scale(1.2);
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2.5rem;
            }
            
            .page-subtitle {
                font-size: 1.2rem;
            }
            
            .navbar {
                top: 10px;
                right: 10px;
                gap: 10px;
            }
            
            .nav-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="navbar">
        <div class="nav-btn" onclick="scrollToSection(0)">Home</div>
        <div class="nav-btn" onclick="scrollToSection(1)">About</div>
        <div class="nav-btn" onclick="scrollToSection(2)">Skills</div>
        <div class="nav-btn" onclick="scrollToSection(3)">Contact</div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator">
        <div class="scroll-dot active" onclick="scrollToSection(0)"></div>
        <div class="scroll-dot" onclick="scrollToSection(1)"></div>
        <div class="scroll-dot" onclick="scrollToSection(2)"></div>
        <div class="scroll-dot" onclick="scrollToSection(3)"></div>
    </div>

    <!-- 流动文字 -->
    <div class="flowing-text" id="flowing-text">
        <h1 class="main-title" id="main-title">X Oberon</h1>
        <p class="sub-title" id="sub-title">Welcome to My Space</p>
        <p class="description" id="description">
            Exploring the intersection of technology, creativity, and innovation.
        </p>
    </div>

    <!-- 滚动容器 -->
    <div class="scroll-container" id="scroll-container">
        <!-- 第1页 - 首页 -->
        <section class="page-section active-section" id="page1">
            <div class="background-text bg-text-1">Developer</div>
            <div class="background-text bg-text-2">Creator</div>
            <div class="background-text bg-text-3">Designer</div>
            <div class="background-text bg-text-4">Innovator</div>
        </section>

        <!-- 第2页 - 关于 -->
        <section class="page-section" id="page2">
            <div class="background-text bg-text-1">Passionate</div>
            <div class="background-text bg-text-2">Dedicated</div>
            <div class="background-text bg-text-3">Professional</div>
            <div class="background-text bg-text-4">Experienced</div>
        </section>

        <!-- 第3页 - 技能 -->
        <section class="page-section" id="page3">
            <div class="background-text bg-text-1">Frontend</div>
            <div class="background-text bg-text-2">Backend</div>
            <div class="background-text bg-text-3">Design</div>
            <div class="background-text bg-text-4">Leadership</div>
        </section>

        <!-- 第4页 - 联系 -->
        <section class="page-section" id="page4">
            <div class="background-text bg-text-1">Connect</div>
            <div class="background-text bg-text-2">Collaborate</div>
            <div class="background-text bg-text-3">Create</div>
            <div class="background-text bg-text-4">Innovate</div>
        </section>
    </div>

    <script>
        // 获取元素
        const scrollContainer = document.getElementById('scroll-container');
        const sections = document.querySelectorAll('.page-section');
        const scrollDots = document.querySelectorAll('.scroll-dot');
        const mainTitle = document.getElementById('main-title');
        const subTitle = document.getElementById('sub-title');
        const description = document.getElementById('description');
        let currentIndex = 0;

        // 页面内容数据
        const pageData = [
            {
                title: "X Oberon",
                subtitle: "Welcome to My Space",
                desc: "Exploring the intersection of technology, creativity, and innovation."
            },
            {
                title: "About Me",
                subtitle: "Passionate Developer & Creator",
                desc: "I'm X Oberon, a dedicated professional focused on creating meaningful digital experiences."
            },
            {
                title: "Skills",
                subtitle: "What I Bring to the Table",
                desc: "Full-Stack Development • UI/UX Design • Creative Problem Solving • Team Leadership"
            },
            {
                title: "Get In Touch",
                subtitle: "Let's Create Something Amazing",
                desc: "Ready to collaborate on your next project? I'm always excited to work with passionate teams."
            }
        ];

        // 滚动到指定页面
        function scrollToSection(index) {
            sections[index].scrollIntoView({ behavior: 'smooth' });
        }

        // 更新流动文字内容
        function updateFlowingText(index) {
            const data = pageData[index];

            // 添加淡出效果
            mainTitle.style.opacity = '0';
            subTitle.style.opacity = '0';
            description.style.opacity = '0';

            setTimeout(() => {
                mainTitle.textContent = data.title;
                subTitle.textContent = data.subtitle;
                description.textContent = data.desc;

                // 淡入效果
                mainTitle.style.opacity = '1';
                subTitle.style.opacity = '0.8';
                description.style.opacity = '1';
            }, 300);
        }

        // 处理滚动事件
        function handleScroll() {
            const scrollPosition = scrollContainer.scrollTop;
            const windowHeight = window.innerHeight;

            // 检测当前页面
            sections.forEach((section, index) => {
                const sectionTop = section.offsetTop - 10;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    if (currentIndex !== index) {
                        // 更新当前页面
                        sections.forEach(s => s.classList.remove('active-section'));
                        section.classList.add('active-section');

                        // 更新滚动指示器
                        scrollDots.forEach(dot => dot.classList.remove('active'));
                        scrollDots[index].classList.add('active');

                        // 更新流动文字
                        updateFlowingText(index);

                        currentIndex = index;
                    }
                }
            });
        }

        // 监听滚动事件
        scrollContainer.addEventListener('scroll', handleScroll);

        // 初始化
        sections[0].classList.add('active-section');
        scrollDots[0].classList.add('active');
        updateFlowingText(0);

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
                scrollToSection(currentIndex + 1);
            } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                scrollToSection(currentIndex - 1);
            }
        });
    </script>
</body>
</html>
