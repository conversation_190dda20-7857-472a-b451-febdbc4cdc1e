<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="HandheldFriendly" content="true">
    <title>X Oberon's Space</title>
    <style>
        /* ==================== 全局样式 ==================== */

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            scrollbar-width: none;
            -ms-overflow-style: none;
            background-color: #f43a47;
            font-family: 'Helvetica', 'Inter', 'Arial', sans-serif;
            overflow-x: hidden;
            position: relative;
            width: 100%;
            scroll-behavior: smooth;
            user-select: none;
            cursor: none;
        }

        /* 自定义光标 */
        .custom-cursor {
            position: absolute;
            width: 1vw;
            height: 1vw;
            border-radius: 50%;
            left: 0;
            top: 0;
            background: #fff;
            margin-left: -1vw;
            margin-top: -1vw;
            mix-blend-mode: difference;
            z-index: 2000;
            pointer-events: none;
        }

        /* 滚动容器 */
        .scroll-container {
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
            z-index: -1;
        }

        /* 页面section */
        .page-section {
            width: 100%;
            height: 100vh;
            position: relative;
            scroll-snap-align: start;
            overflow: hidden;
            z-index: 1;
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }

        .page-section.active-section {
            z-index: 10;
        }

        /* ==================== 页面1样式 ==================== */
        #page1 {
            z-index: 1000;
            height: 100vh;
            overflow: hidden;
        }

        .miniLogo {
            position: absolute;
            width: 8vw;
            left: 25px;
            top: 2vh;
            transition: all 0.5s ease;
        }

        .Logos {
            position: relative;
            top: 3vh;
            left: 2.5vw;
        }

        .thick-bar {
            width: 95vw;
            height: 0.5vh;
            background-color: #000;
        }

        /* 导航栏样式 */
        .topbar-initial {
            position: relative;
            width: 95%;
            margin: 1vh 2.5vw 0;
            height: 4vh;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2%;
            box-sizing: border-box;
            transition: all 0.5s ease;
            z-index: 1000;
        }

        .ohmyking-space {
            font-size: 1.5vh;
            font-weight: 400;
            color: #000;
            position: absolute;
            left: 0;
        }

        .ohmyking {
            font-size: 1.5vh;
            font-weight: 400;
            color: #000;
            position: absolute;
            left: 12vw;
        }

        .position-button-group {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1vw;
            position: absolute;
            right: 0;
        }

        .text-button {
            font-size: 1.5vh;
            font-weight: 400;
            color: #000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .text-button:hover {
            opacity: 0.7;
        }

        .slash1, .slash2 {
            width: 1px;
            height: 2vh;
            background-color: #000;
        }

        .thin-horizontal-bar {
            width: 95vw;
            height: 1px;
            background-color: #000;
            margin-top: 1vh;
        }

        /* 信息区域 */
        .information {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100vw;
            height: 85vh;
            display: flex;
            align-items: flex-end;
            z-index: 1001;
        }

        .thin-vertical-bar {
            width: 1px;
            height: 85vh;
            background-color: #000;
            margin-left: 2.5vw;
        }

        .background-text-ground {
            position: relative;
            margin-left: 2vw;
            height: 85vh;
            width: 60vw;
            overflow: hidden;
        }

        #line1, #line2, #line3, #line4, #line5 {
            position: absolute;
            width: 100%;
            height: 17vh;
            display: flex;
            align-items: center;
            font-size: 12vh;
            font-weight: bold;
            color: #000;
            white-space: nowrap;
        }

        #line1 { top: 0; }
        #line2 { top: 17vh; }
        #line3 { top: 34vh; }
        #line4 { top: 51vh; }
        #line5 { top: 68vh; }

        .learner1, .developer1, .designer1, .creater1, .learner2 {
            position: relative;
            z-index: 2;
            animation: slideRight 20s infinite linear;
        }

        .background-text {
            position: absolute;
            left: 0;
            opacity: 0.1;
            animation: slideRight 20s infinite linear;
            animation-delay: -10s;
        }

        @keyframes slideRight {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100vw); }
        }

        .ohmykings, .space {
            position: absolute;
            right: 5vw;
            font-size: 8vh;
            font-weight: bold;
            color: #000;
            z-index: 1002;
        }

        .ohmykings {
            bottom: 15vh;
        }

        .space {
            bottom: 5vh;
        }
    </style>
</head>
<body style="background-color: rgb(242, 45, 64);">
    <!-- 自定义光标 -->
    <div class="custom-cursor" style="left: 50vw; top: 50vh;"></div>

    <!-- 滚动容器 -->
    <div class="scroll-container" id="scroll-container">
        <!-- 第1页 -->
        <section class="page-section" id="page1">
            <div class="miniLogo">
                <svg fill="none" height="37.000000" viewBox="0 0 125 37" width="125.000000" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.99 0C21.99 0 24.42 2.42 24.42 5.42L24.42 33.88L18.99 33.88L18.99 0Z" fill="#000000" fill-opacity="1.000000" fill-rule="evenodd"></path>
                    <path d="M26.53 17.66C29.53 17.66 31.96 20.09 31.96 23.09L31.96 33.88L26.53 33.88L26.53 17.66Z" fill="#000000" fill-opacity="1.000000" fill-rule="evenodd"></path>
                    <path d="M33.97 0C36.97 0 39.4 2.42 39.4 5.42L39.4 16.22C36.4 16.22 33.97 13.79 33.97 10.79L33.97 0Z" fill="#000000" fill-opacity="1.000000" fill-rule="evenodd"></path>
                    <path d="M41.57 0C44.57 0 47 2.42 47 5.42L47 28.46C47 31.45 44.57 33.88 41.57 33.88L41.57 0Z" fill="#000000" fill-opacity="1.000000" fill-rule="evenodd"></path>
                    <path d="M16.74 0C7.49 0 0 7.61 0 17C0 26.38 7.49 34 16.74 34L16.74 0Z" fill="#F00000" fill-opacity="1.000000" fill-rule="evenodd"></path>
                    <text x="57" y="25" fill="#000000" font-family="Arial" font-size="14" font-weight="bold">X Oberon</text>
                </svg>
            </div>
            <div class="Logos">
                <div class="thick-bar"></div>
            </div>
            <!-- 初始导航栏 -->
            <div class="topbar-initial" id="topbar-initial">
                <div class="ohmyking-space">X Oberon's Space</div>
                <div class="ohmyking">@XOberon</div>
                <div class="position-button-group">
                    <div class="about top-text-button text-button">About</div>
                    <div class="slash1"></div>
                    <div class="work top-text-button text-button">WORK</div>
                    <div class="slash2"></div>
                    <div class="contact top-text-button text-button">CONTACT</div>
                </div>
                <div class="thin-horizontal-bar"></div>
            </div>
            <div class="information">
                <div class="thin-vertical-bar"></div>
                <div class="background-text-ground">
                    <div id="line1">
                        <div class="learner1" style="letter-spacing: 5.45806px;">Learner</div>
                        <div class="background-text" style="letter-spacing: 5.45806px;">DeveloperDesignerCreator</div>
                    </div>
                    <div id="line2">
                        <div class="developer1" style="letter-spacing: 5.32903px;">Developer</div>
                        <div class="background-text" style="letter-spacing: 5.32903px;">DesignerCreatorLearner</div>
                    </div>
                    <div id="line3">
                        <div class="designer1" style="letter-spacing: 5.29677px;">Designer</div>
                        <div class="background-text" style="letter-spacing: 5.29677px;">CreatorLearnerDeveloper</div>
                    </div>
                    <div id="line4">
                        <div class="creater1" style="letter-spacing: 5.45806px;">Creator</div>
                        <div class="background-text" style="letter-spacing: 5.45806px;">LearnerDeveloperDesigner</div>
                    </div>
                    <div id="line5">
                        <div class="learner2" style="letter-spacing: 5.45806px;">Learner</div>
                        <div class="background-text" style="letter-spacing: 5.45806px;">DeveloperDesignerCreator</div>
                    </div>
                </div>
                <div>
                    <div class="ohmykings">X Oberon's</div>
                    <div class="space">SPACE</div>
                </div>
            </div>
        </section>

        <!-- 第2页 - 个人简介 -->
        <section class="page-section" id="page2">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white;">
                <h1 style="font-size: 4rem; margin-bottom: 2rem;">X Oberon</h1>
                <p style="font-size: 1.5rem; opacity: 0.8;">Passionate Developer & Creator</p>
            </div>
        </section>
    </div>

    <script>
        // 获取元素
        const scrollContainer = document.getElementById('scroll-container');
        const sections = document.querySelectorAll('.page-section');
        const customCursor = document.querySelector('.custom-cursor');
        let currentIndex = 0;

        // 自定义光标跟随鼠标
        if (customCursor) {
            document.addEventListener('mousemove', function(event) {
                customCursor.style.left = event.clientX + "px";
                customCursor.style.top = event.clientY + "px";
            });
        }

        // 导航按钮事件
        document.querySelectorAll('.about').forEach(el => el.addEventListener('click', () => scrollToSection(1)));
        document.querySelectorAll('.work').forEach(el => el.addEventListener('click', () => scrollToSection(1)));
        document.querySelectorAll('.contact').forEach(el => el.addEventListener('click', () => scrollToSection(1)));

        // 滚动到指定页面
        function scrollToSection(index) {
            sections[index].scrollIntoView({ behavior: 'smooth' });
        }

        // 处理滚动事件
        function handleScroll() {
            const scrollPosition = scrollContainer.scrollTop;
            const windowHeight = window.innerHeight;

            // 检测当前页面
            sections.forEach((section, index) => {
                const sectionTop = section.offsetTop - 10;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    if (currentIndex !== index) {
                        // 更新当前页面
                        sections.forEach(s => s.classList.remove('active-section'));
                        section.classList.add('active-section');

                        currentIndex = index;
                    }
                }
            });
        }

        // 监听滚动事件
        scrollContainer.addEventListener('scroll', handleScroll);

        // 初始化第一个页面
        sections[0].classList.add('active-section');

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
                scrollToSection(currentIndex + 1);
            } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                scrollToSection(currentIndex - 1);
            }
        });
    </script>
</body>
</html>
